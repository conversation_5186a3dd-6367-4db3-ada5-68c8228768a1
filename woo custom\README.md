# Woo Custom Plugin

A custom WordPress plugin that enhances WooCommerce functionality by adding Wishlist and My Reviews features to the My Account page.

## Features

### 1. Wishlist Functionality
- **Heart Icon on Products**: Displays a heart icon when hovering over products in shop/category pages
- **Add/Remove from Wishlist**: Click the heart icon to add or remove products from personal wishlist
- **Wishlist Page**: Dedicated page in My Account showing all saved products
- **User-Specific Data**: Each user has their own separate wishlist stored in the database
- **Responsive Design**: Works on desktop and mobile devices

### 2. My Reviews Functionality
- **My Reviews Page**: Dedicated page in My Account showing all user's product reviews
- **Review Statistics**: Shows total reviews count and average rating
- **Verified Purchase Badges**: Displays verification status for purchases
- **Product Integration**: Links back to original products and reviews
- **Uses WooCommerce Infrastructure**: Leverages existing WooCommerce review system

## Installation

1. Upload the `woo-custom` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Make sure WooCommerce is installed and activated

## Requirements

- WordPress 6.0 or higher
- WooCommerce 8.0 or higher
- PHP 7.4 or higher

## File Structure

```
woo-custom/
├── woo-custom.php              # Main plugin file
├── includes/
│   ├── class-woo-custom-account.php    # My Account customizations
│   ├── class-woo-custom-wishlist.php   # Wishlist functionality
│   ├── class-woo-custom-reviews.php    # Reviews functionality
│   └── class-woo-custom-ajax.php       # AJAX handlers
├── templates/
│   └── myaccount/
│       ├── wishlist.php        # Wishlist page template
│       └── my-reviews.php      # My Reviews page template
├── assets/
│   ├── css/
│   │   └── woo-custom.css      # Plugin styles
│   └── js/
│       └── woo-custom.js       # Plugin JavaScript
└── README.md                   # This file
```

## Database Tables

The plugin creates one custom table:

### `wp_woo_custom_wishlist`
- `id` (bigint): Primary key
- `user_id` (bigint): WordPress user ID
- `product_id` (bigint): WooCommerce product ID
- `date_added` (datetime): When item was added to wishlist

## Hooks and Filters

### Actions
- `woocommerce_after_shop_loop_item` - Adds wishlist button to product loops
- `woocommerce_single_product_summary` - Adds wishlist button to single product page
- `woocommerce_account_wishlist_endpoint` - Handles wishlist page content
- `woocommerce_account_my-reviews_endpoint` - Handles my reviews page content

### Filters
- `woocommerce_account_menu_items` - Adds custom menu items to My Account
- `woocommerce_endpoint_wishlist_title` - Sets wishlist page title
- `woocommerce_endpoint_my-reviews_title` - Sets my reviews page title

## AJAX Endpoints

### `woo_custom_toggle_wishlist`
Handles adding/removing products from wishlist.

**Parameters:**
- `product_id` (int): Product ID to toggle
- `nonce` (string): Security nonce

**Response:**
```json
{
    "success": true,
    "data": {
        "action": "added|removed",
        "message": "Success message",
        "count": 5
    }
}
```

## Customization

### Styling
The plugin includes comprehensive CSS that can be customized by:
1. Editing `assets/css/woo-custom.css`
2. Adding custom CSS to your theme
3. Using the WordPress Customizer

### Templates
Templates can be overridden by copying them to your theme:
```
your-theme/woocommerce/myaccount/wishlist.php
your-theme/woocommerce/myaccount/my-reviews.php
```

### Functionality
The plugin is built with hooks and filters for easy customization:

```php
// Customize wishlist button text
add_filter('woo_custom_wishlist_button_text', function($text) {
    return 'Save for Later';
});

// Customize reviews per page
add_filter('woo_custom_reviews_per_page', function($per_page) {
    return 20;
});
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Accessibility

The plugin includes:
- ARIA labels and attributes
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Reduced motion support

## Performance

- Optimized database queries
- Minimal JavaScript footprint
- CSS optimized for fast loading
- AJAX requests for smooth user experience

## Security

- Nonce verification for all AJAX requests
- User capability checks
- SQL injection prevention
- XSS protection

## Troubleshooting

### Wishlist buttons not appearing
1. Check if WooCommerce is active
2. Verify user is logged in
3. Check for JavaScript errors in browser console

### My Account pages not working
1. Go to Settings > Permalinks and click "Save Changes"
2. Check if custom endpoints are registered
3. Verify template files exist

### Database issues
1. Deactivate and reactivate the plugin
2. Check database table creation
3. Verify user permissions

## Support

For support and bug reports, please contact the plugin developer.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Wishlist functionality
- My Reviews functionality
- My Account integration
- Responsive design
- AJAX support
