/*
WooCommerce Styles for DmrThema
========
*/

/* Basic WooCommerce Layout */
/* Genel WooCommerce layout - daha spesifik kurallar style.css'de tanimli */
.woocommerce .content-area {
    width: 100%;
}

.woocommerce-breadcrumb {
    padding: 1rem 0;
    margin-bottom: 1rem;
    font-size: 14px;
}

.woocommerce-breadcrumb a {
    color: #333;
    text-decoration: none;
}

.woocommerce-breadcrumb a:hover {
    text-decoration: underline;
}

/* Product Grid */
ul.products {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    list-style: none;
    padding: 0;
}

ul.products li.product {
    padding: 0 15px 30px;
    margin-bottom: 0;
    list-style: none;
    position: relative;
}

@media (min-width: 768px) {
    .columns-2 ul.products li.product {
        width: 50%;
    }
    
    .columns-3 ul.products li.product {
        width: 33.333%;
    }
    
    .columns-4 ul.products li.product {
        width: 25%;
    }
}

@media (max-width: 767px) {
    ul.products li.product {
        width: 50%;
    }
}

/* Product Card */
ul.products li.product img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

ul.products li.product:hover img {
    transform: scale(1.05);
}

.woocommerce-loop-product__title {
    font-size: 16px;
    margin: 10px 0 5px;
    line-height: 1.4;
}

.woocommerce-loop-product__title a {
    color: #333;
    text-decoration: none;
}

.woocommerce-loop-product__title a:hover {
    color: #ff6000;
}

.price {
    font-size: 16px;
    font-weight: bold;
    color: #ff6000;
    margin-bottom: 10px;
}

.price del {
    color: #999;
    font-weight: normal;
}

/* Add to Cart Button */
.button, .add_to_cart_button {
    background: #ff6000;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.button:hover, .add_to_cart_button:hover {
    background: #e55500;
    color: white;
}

/* Urun kartlarindaki sepete ekle butonlari - hover efekti */
ul.products li.product .add_to_cart_button,
ul.products li.product .button {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

ul.products li.product:hover .add_to_cart_button,
ul.products li.product:hover .button {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Sale Badge */
.onsale {
    display: none !important;
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff6000;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
}

/* Shop Banner */
.shop-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

/* Magaza sayfasi basligini gizle */
body.woocommerce-shop .container > h1,
body.woocommerce-page .container > h1 {
    display: none;
}

.shop-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.shop-banner-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.shop-banner-text {
    flex: 1;
    max-width: 600px;
}

.shop-banner-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.shop-banner-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin: 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.shop-banner-image {
    flex-shrink: 0;
    opacity: 0.8;
}

.shop-banner-image svg {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Sorting and Results */
.dmrthema-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 40px;
}

/* WooCommerce orijinal margin kodunu kapat */
.woocommerce .woocommerce-result-count {
    /* margin: 0 0 1em; */
    /* margin: 0 !important; */
}

.woocommerce-result-count {
    color: #666;
    font-size: 16px;
    order: 1;
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: -10px;
}

/* Sıralama ve Filtreler Sağda */
.sorting-filters-wrapper {
    display: flex;
    gap: 15px;
    order: 2;
    flex-shrink: 0;
}

.woocommerce-ordering {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.woocommerce-ordering select {
    padding: 8px 30px 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 40px;
    box-sizing: border-box;
    min-width: 180px;
}

/* Shop Filters Button */
.shop-filters-toggle {
    background: #007cba;
    color: white;
    border: none;
    padding: 0 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: 40px;
    box-sizing: border-box;
    min-width: 120px;
    white-space: nowrap;
}

.shop-filters-toggle:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.shop-filters-toggle i {
    font-size: 12px;
}

/* Shop Filters Toggle States */
.shop-filters-toggle.has-filters {
    background: #28a745 !important;
}

.shop-filters-toggle.has-filters:hover {
    background: #218838 !important;
}

/* Shop Filters Modal */
.shop-filters-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

/* Modal acikken body scroll'unu engelle - daha iyi yontem */
body.modal-open {
    overflow: hidden !important;
    /* position: fixed kaldiriliyor - header/footer bozulmasini onlemek icin */
}

/* HTML elementine de overflow hidden ekle - tam scroll engelleme icin */
html.modal-open {
    overflow: hidden !important;
}

.shop-filters-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.shop-filters-modal-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.shop-filters-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.shop-filters-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.shop-filters-close:hover {
    background: #e9ecef;
    color: #333;
}

.shop-filters-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.filter-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-group h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.price-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-filter input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.price-filter span {
    color: #666;
    font-weight: 500;
}

.filter-checkbox {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    transition: color 0.2s ease;
}

.filter-checkbox:hover {
    color: #007cba;
}

.filter-checkbox input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #007cba;
}

.filter-checkbox span {
    flex: 1;
}

.shop-filters-modal-footer {
    background: #f8f9fa;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.btn-clear-filters,
.btn-apply-filters {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.btn-clear-filters {
    background: #6c757d;
    color: white;
}

.btn-clear-filters:hover {
    background: #5a6268;
}

.btn-apply-filters {
    background: #007cba;
    color: white;
}

.btn-apply-filters:hover {
    background: #005a87;
}

/* Responsive Design for Filters */
@media (max-width: 768px) {
    .shop-filters-modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 90vh;
    }

    .dmrthema-sorting {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .shop-filters-toggle {
        padding: 0 12px;
        font-size: 13px;
        height: 36px;
        min-width: 100px;
    }

    .woocommerce-ordering select {
        padding: 8px 30px 8px 12px;
        background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        height: 36px;
        min-width: 150px;
    }

    .price-filter {
        flex-direction: column;
        gap: 8px;
    }

    .price-filter input {
        width: 100%;
    }

    .shop-filters-modal-footer {
        flex-direction: column;
    }
}

/* Pagination */
.woocommerce-pagination {
    text-align: center;
    margin-top: 30px;
}

.woocommerce-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: #333;
    text-decoration: none;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.woocommerce-pagination .page-numbers:hover,
.woocommerce-pagination .page-numbers.current {
    background: #ff6000;
    color: white;
    border-color: #ff6000;
}

/* Messages */
.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.woocommerce-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.woocommerce-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.woocommerce-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Cart */
.cart-contents {
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
}

.cart-contents:hover {
    color: #ff6000;
}

.cart-contents .count {
    display: none !important;
}

/* Product Categories */
ul.products li.product.product-category h2 {
    text-align: center;
    margin-top: 15px;
}

ul.products li.product.product-category a {
    display: block;
    text-decoration: none;
    color: #333;
}

ul.products li.product.product-category a:hover {
    color: #ff6000;
}

/* Star Rating */
.star-rating {
    font-size: 14px;
    color: #ff6000;
    margin-bottom: 5px;
}

/* Out of Stock */
.product-out-of-stock {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .shop-banner {
        padding: 40px 0;
        margin-bottom: 30px;
    }

    .shop-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .shop-banner-title {
        font-size: 2.5rem;
    }

    .shop-banner-description {
        font-size: 1.1rem;
    }

    .shop-banner-image svg {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 480px) {
    ul.products li.product {
        width: 100%;
    }

    .dmrthema-sorting {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
    }

    .woocommerce-ordering {
        justify-content: center;
        width: 100%;
    }

    .woocommerce-ordering select {
        padding: 8px 30px 8px 12px;
        background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        min-width: auto;
    }

    .shop-filters-toggle {
        width: 100%;
        justify-content: center;
        min-width: auto;
    }

    .shop-banner {
        padding: 30px 0;
        margin-bottom: 20px;
    }

    .shop-banner-title {
        font-size: 2rem;
    }

    .shop-banner-description {
        font-size: 1rem;
    }

    .shop-banner-image svg {
        width: 80px;
        height: 80px;
    }
}

/* SIDEBAR MINI CART - TUTARLI GORUNUM */
/* En yuksek oncelikli kurallar - tum CSS cakismalarini onler */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Tum sayfa tipleri icin ayni kurallar */
html body.home .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

/*
WooCommerce My Account Styles
=============================
*/
@media (min-width: 993px) {
    .woocommerce-account .content-area {
        width: 100%;
    }
    .woocommerce-MyAccount-navigation {
        float: left;
        width: 20% !important;
        margin-right: 5.**********%;
    }
    .woocommerce-MyAccount-content {
        float: right;
        width: 70% !important;
        padding-top: 0 !important;
    }
    .col2-set#customer_login,
    .col2-set.addresses {
        float: left;
        width: 100%;
        margin-right: 0;
        margin-left: 0
    }
    .col2-set#customer_login .col-1,
    .col2-set.addresses .col-1 {
        float: left;
        width: 41.**********%;
        margin-right: 5.**********%
    }
    .col2-set#customer_login .col-2,
    .col2-set.addresses .col-2 {
        float: right;
        width: 52.**********%;
        margin-right: 0
    }
    .woocommerce-MyAccount-content .form-row-first {
        float: left;
        width: 42.**********%;
        margin-right: 3.**********%;
    }
    .woocommerce-MyAccount-content .form-row-last {
        float: right;
        width: 53.**********%;
        margin-right: 0;
    }
}

.woocommerce-MyAccount-content {
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}

/* -- Login/Register -- */
#customer_login p {
    font-size: 14px;
}
.woocommerce-ResetPassword p {
    font-size: 15px;
}
#customer_login p a {
    font-weight: bold;
    text-decoration: underline;
    text-underline-offset: 0.12em;
    text-decoration-thickness: 0.75px;
}
.form-row input.woocommerce-form__input-checkbox {
    float: left;
    /* margin: 5px 2px 0 0; */ /* Kapatildi */
}
.woocommerce-account .woocommerce-form-login__rememberme span {
    margin-left: 0.5rem;
}

/* WooCommerce Giris Sayfasi - Remember Me Display Inline-Block Kapat */
.woocommerce .woocommerce-form-login .woocommerce-form-login__rememberme {
    /* display: inline-block; */ /* WooCommerce'den gelen kod kapatildi */
}

/* WooCommerce Giris Sayfasi - Beni Hatirla ve Giris Yap Butonu Hizalama */
.woocommerce .woocommerce-form-login .form-row.form-row-wide {
    display: block !important; /* Normal form alanları için */
}

.woocommerce .woocommerce-form-login .form-row:not(.form-row-wide) {
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    margin-top: 15px;
}

.woocommerce .woocommerce-form-login .woocommerce-form-login__rememberme {
    margin-left: 15px;
    flex-shrink: 0 !important;
    order: 2 !important; /* Beni hatirla sağda */
}

.woocommerce .woocommerce-form-login .woocommerce-form-login__submit {
    margin: 0 !important;
    flex-shrink: 0 !important;
    order: 1 !important; /* Giriş yap solda */
}
.woocommerce-account .col2-set .button {
    margin-top: 1rem;
}
.woocommerce-privacy-policy-text p {
    margin: 0;
}

/* -- Navigation -- */
.woocommerce-account .woocommerce-MyAccount-navigation {
    padding-left: 20px;
    padding-right: 20px;
    background-color: #F8F8F8;
    border-radius: 15px;
}

.woocommerce-MyAccount-navigation ul {
    margin-left: 0;
}
.woocommerce-MyAccount-navigation ul li {
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    list-style: none;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}
.woocommerce-MyAccount-navigation ul li:last-child {
    border: none;
}
.woocommerce-MyAccount-navigation ul li a {
    color: #555;
    display: block;
    padding: 0.675em 0 0.775em 0;
    text-decoration: none;
}
.woocommerce-MyAccount-navigation li a:hover,
.woocommerce-MyAccount-navigation li.is-active a {
    color: #222;
}
.woocommerce-MyAccount-navigation li.is-active a {
    font-weight: 600;
}
.woocommerce-MyAccount-navigation ul li a:before {
    background: #111;
    position: relative;
    top: 0 !important;
    float: right;
    content: " ";
    width: 20px;
    height: 20px;
    opacity: 0.35;
    -webkit-mask-position: center;
            mask-position: center;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: contain;
            mask-size: contain;
    transition: 0.2s all;
    display: inline-block;
}
.woocommerce-MyAccount-navigation ul li a:hover:before,
.woocommerce-MyAccount-navigation ul li.is-active a:before {
    opacity: 1;
}

/* -- Icons -- */
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--dashboard a:before {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><path d="M0 0h24v24H0z"/><rect width="16" height="16" x="4" y="4" stroke="%********" stroke-linecap="round" stroke-width="1.5" rx="2"/><path d="M4 9h16M9 10v10" stroke="%********" stroke-linecap="round" stroke-width="1.5"/></g></svg>');
            mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><path d="M0 0h24v24H0z"/><rect width="16" height="16" x="4" y="4" stroke="%********" stroke-linecap="round" stroke-width="1.5" rx="2"/><path d="M4 9h16M9 10v10" stroke="%********" stroke-linecap="round" stroke-width="1.5"/></g></svg>');
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--orders a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--downloads a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 12L12 16M12 16L8 12M12 16L12 4' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 12L12 16M12 16L8 12M12 16L12 4' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-address a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--payment-methods a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 10H21M7 15H8M12 15H13M6 19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 10H21M7 15H8M12 15H13M6 19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-account a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.12104 17.8037C7.15267 16.6554 9.4998 16 12 16C14.5002 16 16.8473 16.6554 18.879 17.8037M15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10ZM21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.12104 17.8037C7.15267 16.6554 9.4998 16 12 16C14.5002 16 16.8473 16.6554 18.879 17.8037M15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10ZM21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-logout a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 16L21 12M21 12L17 8M21 12L7 12M13 16V17C13 18.6569 11.6569 20 10 20H6C4.34315 20 3 18.6569 3 17V7C3 5.34315 4.34315 4 6 4H10C11.6569 4 13 5.34315 13 7V8' stroke='%********' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 16L21 12M21 12L17 8M21 12L7 12M13 16V17C13 18.6569 11.6569 20 10 20H6C4.34315 20 3 18.6569 3 17V7C3 5.34315 4.34315 4 6 4H10C11.6569 4 13 5.34315 13 7V8' stroke='%********' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
/* Wishlist Icon - Kalp Iconu */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:before,
body.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
body.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 3C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.041 1.55 8.5C1.5487 9.959 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.45 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 3C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.041 1.55 8.5C1.5487 9.959 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.45 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--subscriptions a:before,
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wc-smart-coupons a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8C10.3431 8 9 8.89543 9 10C9 11.1046 10.3431 12 12 12C13.6569 12 15 12.8954 15 14C15 15.1046 13.6569 16 12 16M12 8C13.1104 8 14.0799 8.4022 14.5987 9M12 8V7M12 8L12 16M12 16L12 17M12 16C10.8896 16 9.92008 15.5978 9.40137 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8C10.3431 8 9 8.89543 9 10C9 11.1046 10.3431 12 12 12C13.6569 12 15 12.8954 15 14C15 15.1046 13.6569 16 12 16M12 8C13.1104 8 14.0799 8.4022 14.5987 9M12 8V7M12 8L12 16M12 16L12 17M12 16C10.8896 16 9.92008 15.5978 9.40137 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* -- Links -- */
.woocommerce-account .woocommerce-MyAccount-content p a,
.addresses header.title a {
    text-decoration: underline;
    text-decoration-thickness: 0.75px;
    text-underline-offset: 0.12em;
}

/* -- Headings -- */
.woocommerce-account h2 {
    font-size: clamp(1.375rem, 1.1209rem + 0.813vw, 1.625rem); /* 22-26 */
}

/* -- Order Details Table List -- */
.woocommerce-account .woocommerce-orders-table th {
    padding: 10px 15px 10px 0;
    border-bottom: 1px solid #eee;
    color: #222;
    font-size: 14px;
}
.woocommerce-account table td {
    font-size: 14px;
}
.woocommerce-account tr.woocommerce-orders-table__row td {
    padding: 10px 15px 10px 0;
    border-bottom: 1px solid #f2f2f2;
}
.woocommerce-account tr.woocommerce-orders-table__row td.woocommerce-orders-table__cell-order-actions {
    text-align: right;
    padding-right: 0;
}
.woocommerce-account .woocommerce-orders-table tr th:first-child,
.woocommerce-account .woocommerce-orders-table tr td:first-child {
    padding-left: 0;
}
.woocommerce-account .woocommerce-orders-table tr th:last-child {
    text-align: right;
    padding-right: 0;
}
td.woocommerce-orders-table__cell-order-number a {
    font-weight: bold;
    text-decoration: underline;
}
tr.woocommerce-orders-table__row--status-completed td.woocommerce-orders-table__cell-order-status {
    color: #25841e;
    font-weight: 600;
}
.my_account_orders .button,
.woocommerce-MyAccount-downloads .button {
    margin-right: 0.236em;
    padding: 0.6180469716em 0.875em;
    font-size: 12px;
}

/* -- Downloads -- */
.woocommerce-account .woocommerce-order-downloads table thead th {
    padding: 0 20px 10px 0;
    border-bottom: 1px solid #eee;
}
.woocommerce-account .woocommerce-order-downloads td {
    padding: 10px 20px 10px 0;
    border-bottom: 1px solid #eee;
}
.woocommerce-account .woocommerce-order-downloads a.button.alt {
    font-size: 13px;
    margin: 0;
}

/* -- Pagination -- */
.woocommerce-MyAccount-content .woocommerce-Pagination {
    float: none;
    text-align: center;
    display: flex;
    justify-content: space-between;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a {
    padding: 0.7em 1.5em;
    background-color: #fff;
    color: #111;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 600;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a:only-child {
    margin-left: auto;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a:hover {
    border-color: #ccc;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a.woocommerce-button--previous:before {
    margin-right: 5px;
    content: "\2190";
}
.woocommerce-MyAccount-content .woocommerce-Pagination a.woocommerce-button--next:after {
    margin-left: 5px;
    content: "\2192";
}

/* -- Single Order Details -- */
.woocommerce-order-details {
    margin-bottom: 2rem;
    padding: 2rem;
    border: 1px solid #e2e2e2;
}
.woocommerce-order-details table {
    margin-bottom: 0;
    font-size: 14px;
}
.woocommerce-order-details table th,
.woocommerce-order-details table td {
    padding: 0.5rem 0;
}
.woocommerce-order-details table a {
    font-weight: 600;
}
.woocommerce-order-details table strong {
    font-weight: normal;
}
.woocommerce-order-details table tfoot th,
.woocommerce-order-details table tfoot td {
    border-bottom: none;
    padding-bottom: 0;
}
.woocommerce-order-details th,
.woocommerce-order-details td {
    padding-top: 10px;
    border-bottom: 1px solid #eee;
}
.woocommerce-order-details th:last-child,
.woocommerce-order-details td:last-child {
    text-align: right;
}
.woocommerce-order-details tfoot tr:last-child th,
.woocommerce-order-details tfoot tr:last-child td {
    border: none;
    font-size: 18px;
    font-weight: bold;
}
.woocommerce-account .entry-content:not(.wc-tab) .woocommerce-order-details p {
    font-size: 13px;
}
.woocommerce-order-details .wc-item-meta {
    margin-top: 5px;
    margin-bottom: 0px;
}
.woocommerce-order-details .wc-item-meta li,
.woocommerce-account .entry-content:not(.wc-tab) .woocommerce-order-details .wc-item-meta p {
    font-size: 12px;
}
.woocommerce-order-details .wc-item-meta li {
    margin-bottom: 0;
}
.woocommerce-MyAccount-content mark {
    font-weight: 600;
}
.wc-item-meta {
    margin-top: 10px;
    margin-left: 0;
    font-size: 0.875em;
    list-style: none;
}
.wc-item-meta li p, .wc-item-meta li strong {
    display: inline-block;
    margin: 0;
}
.woocommerce-account .woocommerce-MyAccount-content p.order-again {
    margin: 1.5rem 0 0 0;
}
.woocommerce-account .woocommerce-MyAccount-content p.order-again a {
    text-decoration: none;
    font-size: 14px;
}

/* -- Addresses -- */
.woocommerce-Address-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}
.woocommerce-Address-title h3 {
    margin-bottom: 0;
}
.addresses header.title a {
    font-size: 14px;
    margin-left: 0.5rem;
}
.woocommerce-account p#billing_address_1_field,
.woocommerce-account p#shipping_address_1_field {
    margin-bottom: 0.5rem;
}

/* -- Account Details -- */
.woocommerce-account fieldset {
    margin: 0;
    padding: 0;
}
.woocommerce-MyAccount-content p em {
    display: inline-block;
    margin-top: 0.5rem;
    font-size: 12px;
    font-style: normal;
}

/* -- Notices -- */
.woocommerce-account .woocommerce-info .button {
    font-size: 14px;
}

/* -- Password Strength -- */
.woocommerce-password-hint {
    display: block;
    font-size: 12px;
    padding-top: 0.5rem;
}

/* -- Hesabim Sayfasi Gizlenen Elementler -- */
#post-15 > div > div > div > p:nth-child(2),
#post-15 > div > div > div > p:nth-child(3) {
    display: none !important;
}
.woocommerce form .password-input {
    position: relative;
}
.woocommerce form .password-input input[type="password"] {
    padding-right: 2.5rem;
}
.woocommerce-page form .show-password-input {
    position: absolute;
    right: 0.7em;
    top: 0px;
    cursor: pointer;
}
.woocommerce-page form .show-password-input:after {
    content: '';
    display: none !important; /* Gizle */
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z' /%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z' /%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z' /%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z' /%3E%3C/svg%3E");
    -webkit-mask-position: 50%;
            mask-position: 50%;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: 1em;
            mask-size: 1em;
    min-height: 1.2em;
    min-width: 1.2em;
    top: 0;
    z-index: 1;
    background: #999;
}
.woocommerce-password-strength {
    padding: 0.5407911001em 0 0 0;
    font-size: 0.875em;
}
.woocommerce-password-strength.strong {
    color: #0f834d;
}
.woocommerce-password-strength.bad,
.woocommerce-password-strength.short {
    color: #e2401c;
}
.woocommerce-password-strength.good {
    color: #3d9cd2;
}

/* -- My Account Responsive Design -- */
@media screen and (max-width: 992px) {
    .woocommerce-account .site-content {
        padding-bottom: 3rem;
    }
    .u-column2.col-2 {
        margin-top: 2rem;
    }
    .woocommerce-MyAccount-content table {
        margin: 0;
    }
    .woocommerce-MyAccount-content table thead {
        display: none;
    }
    .woocommerce-MyAccount-content table tr {
        display: block;
        margin-bottom: 0.625em;
    }
    .woocommerce-MyAccount-content table td {
        display: block;
    }
    .woocommerce-MyAccount-content table td:before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
    }
    .woocommerce-MyAccount-content table td:last-child {
        border-bottom: 0;
    }
    .woocommerce-order-details table tfoot th, .woocommerce-order-details table tfoot td {
        padding-bottom: 0.5rem;
    }
    .woocommerce-order-details td.woocommerce-table__product-total,
    .woocommerce-order-details tfoot td:last-child {
        text-align: left;
    }
    .woocommerce-account .order_details tfoot tr td {
        border-top: 1px solid #eee;
    }
    .woocommerce-account .order_details tfoot tr:first-child th,
    .woocommerce-order-details th {
        padding-bottom: 0.5rem;
    }
    .woocommerce-account tr.woocommerce-orders-table__row td.woocommerce-orders-table__cell-order-actions {
        text-align: left;
    }
}

/* columns-3 icindeki div'lere dmrthema-sorting stilini uygula */
body > div.container > div.columns-3 > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 40px;
}

/* columns-3 icindeki div'lerin responsive tasarimi */
@media (max-width: 768px) {
    body > div.container > div.columns-3 > div {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    body > div.container > div.columns-3 > div .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    body > div.container > div.columns-3 > div .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    body > div.container > div.columns-3 > div .shop-filters-toggle {
        padding: 0 12px;
        font-size: 13px;
        height: 36px;
        min-width: 100px;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering select {
        padding: 8px 30px 8px 12px;
        background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        height: 36px;
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    body > div.container > div.columns-3 > div {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    body > div.container > div.columns-3 > div .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    body > div.container > div.columns-3 > div .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering {
        justify-content: center;
        width: 100%;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering select {
        padding: 8px 30px 8px 12px;
        background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        min-width: auto;
    }

    body > div.container > div.columns-3 > div .shop-filters-toggle {
        width: 100%;
        justify-content: center;
        min-width: auto;
    }
}

/* columns-3 icindeki div'lerin icerigini duzenle */
body > div.container > div.columns-3 > div .woocommerce-result-count {
    color: #666;
    font-size: 16px;
    order: 1;
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: -10px;
}

body > div.container > div.columns-3 > div .sorting-filters-wrapper {
    display: flex;
    gap: 15px;
    order: 2;
    flex-shrink: 0;
}

body > div.container > div.columns-3 > div .woocommerce-ordering {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

body > div.container > div.columns-3 > div .woocommerce-ordering select {
    padding: 8px 30px 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 40px;
    box-sizing: border-box;
    min-width: 180px;
}

/* Checkout Order Summary - Urun Aciklamasi Tasma Sorunu */
.wc-block-components-order-summary-item__description {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    max-width: 100% !important;
    overflow: hidden !important;
}

.wc-block-components-order-summary-item__description div {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    max-width: 100% !important;
    overflow: hidden !important;
    line-height: 1.4 !important;
}

/* Checkout sidebar genislik kontrolu */
.wc-block-checkout__sidebar {
    overflow: hidden !important;
}

.wc-block-components-totals-wrapper {
    overflow: hidden !important;
    word-wrap: break-word !important;
}

/* WISHLIST ICON - KALP ICONU - GUCLU KURAL */
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:before {
    content: " " !important;
    display: inline-block !important;
    background: #111 !important;
    width: 20px !important;
    height: 20px !important;
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 3C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.041 1.55 8.5C1.5487 9.959 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.45 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 3C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.041 1.55 8.5C1.5487 9.959 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.45 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
    -webkit-mask-position: center !important;
            mask-position: center !important;
    -webkit-mask-repeat: no-repeat !important;
            mask-repeat: no-repeat !important;
    -webkit-mask-size: contain !important;
            mask-size: contain !important;
    opacity: 0.35 !important;
    float: right !important;
    position: relative !important;
    top: 0 !important;
    transition: 0.2s all !important;
}

/* Wishlist Icon Hover Efekti */
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist.is-active a:before {
    opacity: 1 !important;
}

/* WooCommerce Bildirim Alanlarini Gizle */
/* Hesabim sayfasinda bildirim alanini gizle */
body.woocommerce-account #post-15 > div > div > div > div.woocommerce-notices-wrapper {
    display: none !important;
}

/* WooCommerce Reviews Comments List */
.woocommerce #reviews #comments ol.commentlist {
    padding-left: 0px;
}

/* WooCommerce Reviews Avatar Styling */
.woocommerce #reviews #comments ol.commentlist li img.avatar {
    background: none !important;
    border: none !important;
    border-radius: 50% !important;
    width: 45px;
    height: 45px;
    object-fit: cover;
}

/* WooCommerce Reviews Comment Text Margin */
.woocommerce #reviews #comments ol.commentlist li .comment-text {
    margin: 0 0 0 65px;
}

/* REVIEWS ICON - YILDIZ ICONU - GUCLU KURAL */
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews a:before {
    content: " " !important;
    display: inline-block !important;
    background: #111 !important;
    width: 20px !important;
    height: 20px !important;
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolygon points='12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolygon points='12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
    -webkit-mask-position: center !important;
            mask-position: center !important;
    -webkit-mask-repeat: no-repeat !important;
            mask-repeat: no-repeat !important;
    -webkit-mask-size: contain !important;
            mask-size: contain !important;
    opacity: 0.35 !important;
    float: right !important;
    position: relative !important;
    top: 0 !important;
    transition: 0.2s all !important;
}

/* Reviews Icon Hover Efekti */
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews a:hover:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews.is-active a:before,
html body.woocommerce-account #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--reviews.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-reviews.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--degerlendirmeler.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--review.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews.is-active a:before,
html body #post-15 .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--user-reviews.is-active a:before {
    opacity: 1 !important;
}

/* ========================================
   HESAP DETAYLARI SAYFASI - MODERN TASARIM
   SADECE EDIT-ACCOUNT SAYFASI ICIN GECERLI
   ======================================== */

/* Ana container styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-radius: 20px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Dekoratif arka plan elementi - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div:before {
    content: '' !important;
    position: absolute !important;
    top: -50% !important;
    right: -50% !important;
    width: 200% !important;
    height: 200% !important;
    background: radial-gradient(circle, rgba(255, 96, 0, 0.05) 0%, transparent 70%) !important;
    z-index: 0 !important;
    pointer-events: none !important;
}

/* Form container - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div > form {
    position: relative !important;
    z-index: 1 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 15px !important;
    padding: 30px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Baslik styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div h2,
body.woocommerce-edit-account #post-15 > div > div > div h3 {
    color: #2c3e50 !important;
    font-weight: 700 !important;
    margin-bottom: 25px !important;
    text-align: center !important;
    position: relative !important;
}

body.woocommerce-edit-account #post-15 > div > div > div h2:after,
body.woocommerce-edit-account #post-15 > div > div > div h3:after {
    content: '' !important;
    position: absolute !important;
    bottom: -10px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 60px !important;
    height: 3px !important;
    background: linear-gradient(90deg, #ff6000, #ff8533) !important;
    border-radius: 2px !important;
}

/* Form row styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row {
    margin-bottom: 25px !important;
    position: relative !important;
}

/* Label styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 600 !important;
    color: #34495e !important;
    font-size: 14px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Input styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row input[type="text"],
body.woocommerce-edit-account #post-15 > div > div > div .form-row input[type="email"],
body.woocommerce-edit-account #post-15 > div > div > div .form-row input[type="password"],
body.woocommerce-edit-account #post-15 > div > div > div .form-row input[type="tel"],
body.woocommerce-edit-account #post-15 > div > div > div .form-row select,
body.woocommerce-edit-account #post-15 > div > div > div .form-row textarea {
    width: 100% !important;
    padding: 15px 20px !important;
    border: 2px solid #e1e8ed !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    background: #ffffff !important;
    transition: none !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .form-row input:focus,
body.woocommerce-edit-account #post-15 > div > div > div .form-row select:focus,
body.woocommerce-edit-account #post-15 > div > div > div .form-row textarea:focus {
    outline: none !important;
    border-color: #ff6000 !important;
    box-shadow: 0 0 0 3px rgba(255, 96, 0, 0.1) !important;
    transform: none !important;
}

/* Required field indicator - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row .required {
    color: #e74c3c !important;
    font-weight: bold !important;
}

/* Form row grid layout - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row-first,
body.woocommerce-edit-account #post-15 > div > div > div .form-row-last {
    width: 48% !important;
    display: inline-block !important;
    vertical-align: top !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .form-row-first {
}

/* Button styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .button,
body.woocommerce-edit-account #post-15 > div > div > div input[type="submit"] {
    background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%) !important;
    color: white !important;
    border: none !important;
    padding: 15px 40px !important;
    border-radius: 50px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    cursor: pointer !important;
    transition: none !important;
    box-shadow: 0 5px 15px rgba(255, 96, 0, 0.3) !important;
    display: inline-block !important;
    text-decoration: none !important;
    margin-top: 20px !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .button:hover,
body.woocommerce-edit-account #post-15 > div > div > div input[type="submit"]:hover {
    transform: none !important;
    box-shadow: 0 5px 15px rgba(255, 96, 0, 0.3) !important;
    background: linear-gradient(135deg, #e55a00 0%, #ff7420 100%) !important;
}

/* Fieldset styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div fieldset {
    border: 2px solid #e1e8ed !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin-bottom: 30px !important;
    background: rgba(248, 249, 250, 0.5) !important;
}

body.woocommerce-edit-account #post-15 > div > div > div fieldset legend {
    background: #ffffff !important;
    padding: 10px 20px !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    border: 2px solid #e1e8ed !important;
    margin-bottom: 15px !important;
}

/* Password strength indicator - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .woocommerce-password-strength {
    margin-top: 10px !important;
    padding: 8px 15px !important;
    border-radius: 8px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Success message styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .woocommerce-message {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 15px 20px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3) !important;
}

/* Error message styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .woocommerce-error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 15px 20px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3) !important;
}

/* Responsive design for mobile - SADECE hesap detaylari sayfasi */
@media (max-width: 768px) {
    body.woocommerce-edit-account #post-15 > div > div > div {
        padding: 25px 20px !important;
        margin: 10px 0 !important;
        border-radius: 15px !important;
    }

    body.woocommerce-edit-account #post-15 > div > div > div > form {
        padding: 20px !important;
        border-radius: 12px !important;
    }

    body.woocommerce-edit-account #post-15 > div > div > div .form-row-first,
    body.woocommerce-edit-account #post-15 > div > div > div .form-row-last {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 20px !important;
    }

    body.woocommerce-edit-account #post-15 > div > div > div .form-row input,
    body.woocommerce-edit-account #post-15 > div > div > div .form-row select,
    body.woocommerce-edit-account #post-15 > div > div > div .form-row textarea {
        padding: 12px 15px !important;
        font-size: 16px !important;
    }

    body.woocommerce-edit-account #post-15 > div > div > div .button,
    body.woocommerce-edit-account #post-15 > div > div > div input[type="submit"] {
        width: 100% !important;
        padding: 15px 20px !important;
        font-size: 16px !important;
    }
}

/* Hover effects for form elements - SADECE hesap detaylari sayfasi - ANIMASYONLAR KALDIRILDI */
body.woocommerce-edit-account #post-15 > div > div > div .form-row {
    transition: none !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .form-row:hover {
    transform: none !important;
}

/* Custom checkbox and radio styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div input[type="checkbox"],
body.woocommerce-edit-account #post-15 > div > div > div input[type="radio"] {
    width: 20px !important;
    height: 20px !important;
    margin-right: 10px !important;
    accent-color: #ff6000 !important;
}

/* Loading animation for form submission - SADECE hesap detaylari sayfasi - ANIMASYONLAR KALDIRILDI */
body.woocommerce-edit-account #post-15 > div > div > div .button:active,
body.woocommerce-edit-account #post-15 > div > div > div input[type="submit"]:active {
    transform: none !important;
}

/* Form validation styling - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .form-row input.woocommerce-invalid {
    border-color: #e74c3c !important;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .form-row input.woocommerce-validated {
    border-color: #27ae60 !important;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1) !important;
}

/* Password visibility toggle - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .password-input {
    position: relative !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .show-password-input {
    position: absolute !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
    color: #7f8c8d !important;
    transition: none !important;
}

body.woocommerce-edit-account #post-15 > div > div > div .show-password-input:hover {
    color: #ff6000 !important;
}

/* Form description text - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .description {
    font-size: 12px !important;
    color: #7f8c8d !important;
    margin-top: 5px !important;
    font-style: italic !important;
}

/* Privacy policy text - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div .woocommerce-privacy-policy-text {
    background: rgba(52, 73, 94, 0.05) !important;
    padding: 15px !important;
    border-radius: 10px !important;
    margin-top: 20px !important;
    font-size: 13px !important;
    line-height: 1.6 !important;
}

/* Form section dividers - SADECE hesap detaylari sayfasi */
body.woocommerce-edit-account #post-15 > div > div > div hr {
    border: none !important;
    height: 2px !important;
    background: linear-gradient(90deg, transparent 0%, #e1e8ed 50%, transparent 100%) !important;
    margin: 30px 0 !important;
}

/* Profile Photos Section - Belirtilen CSS kurallarini kaldir */
.woo-custom-profile-photos-section {
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Hesap detaylari sayfasi - Parola input after iconlarini gizle */
body.woocommerce-edit-account #post-15 > div > div > div > form > fieldset > p:nth-child(2) > span > button:after,
body.woocommerce-edit-account #post-15 > div > div > div > form > fieldset > p > span > button:after,
body.woocommerce-edit-account .password-input button:after,
body.woocommerce-edit-account .show-password-input:after {
    display: none !important;
    content: none !important;
}

/* WooCommerce Giris Sayfasi - Show Password Input Before */
.woocommerce form .show-password-input::before,
.woocommerce-page form .show-password-input::before {
    margin-top: 28px;
}

/* WooCommerce Giris Sayfasi - Beni Hatirla Label Display Flex */
#customer_login > div.u-column1.col-1 > form > p:nth-child(3) > label {
    display: flex;
}

/* Hesap Detaylari Sayfasi - Show Password Input Before */
body.woocommerce-edit-account .woocommerce form .show-password-input::before,
body.woocommerce-edit-account .woocommerce-page form .show-password-input::before {
    margin-top: 0px !important;
}
