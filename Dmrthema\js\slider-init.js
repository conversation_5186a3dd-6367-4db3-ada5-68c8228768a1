document.addEventListener('DOMContentLoaded', function () {
    // WordPress'ten slider ayarlarını al
    const sliderSettings = window.dmrthemaSliderSettings || {};
    const autoplayEnabled = sliderSettings.autoplay === '1';
    const autoplayDelay = parseInt(sliderSettings.delay) || 5000;

    // Swiper konfigürasyonu
    const swiperConfig = {
        // Swiper ayarları
        loop: true,

        // İleri/geri butonları
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        // Sayfalama noktaları
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
    };

    // Otomatik geçiş ayarı varsa ekle
    if (autoplayEnabled) {
        swiperConfig.autoplay = {
            delay: autoplayDelay,
            disableOnInteraction: false, // Kullanıcı etkileşiminde durmasın
        };
    }

    const swiper = new Swiper('.main-slider', swiperConfig);

    // Egitmenler slider'i icin ayri konfigürasyon
    const instructorsSwiper = new Swiper('.instructors-slider', {
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        },
        navigation: {
            nextEl: '.instructors-next',
            prevEl: '.instructors-prev',
        },
        breakpoints: {
            480: {
                slidesPerView: 2,
                spaceBetween: 0,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 2,
            },
            1024: {
                slidesPerView: 4,
                spaceBetween: 3,
            },
            1200: {
                slidesPerView: 5,
                spaceBetween: 5,
            },
            1400: {
                slidesPerView: 6,
                spaceBetween: 5,
            }
        }
    });
});
