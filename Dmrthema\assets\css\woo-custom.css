/**
 * Woo Custom Plugin Styles
 */

/* Font Awesome CDN Import */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* Wishlist Button Styles */
.woo-custom-wishlist-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Heart icon styles */
.woo-custom-wishlist-btn .heart-icon,
.woo-custom-wishlist-btn .heart-icon-filled {
    font-size: 20px;
    transition: all 0.3s ease;
    display: inline-block;
}

.woo-custom-wishlist-btn .heart-icon {
    color: #999;
}

.woo-custom-wishlist-btn .heart-icon-filled {
    color: #e74c3c;
    display: none;
}

/* Hover states */
.woo-custom-wishlist-btn:hover .heart-icon {
    color: #e74c3c;
    transform: scale(1.1);
}

/* Active state (in wishlist) */
.woo-custom-wishlist-btn.in-wishlist .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn.in-wishlist .heart-icon-filled {
    display: inline-block;
    animation: heartBeat 0.6s ease-in-out;
}

/* Heart beat animation */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Product loop positioning */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woocommerce ul.products li.product:hover .woo-custom-wishlist-btn {
    opacity: 1;
    transform: translateY(0);
}

/* Single product page wishlist button */
.woo-custom-wishlist-wrapper {
    margin: 15px 0;
}

.woo-custom-wishlist-btn.single-product {
    background: none;
    border: none;
    padding: 0;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    margin-left: 30px;
    white-space: nowrap;
    width: 110px;
    outline: none;
}

.woo-custom-wishlist-btn.single-product:focus {
    outline: none;
    box-shadow: none;
}

.woo-custom-wishlist-btn.single-product:hover {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product:hover .heart-icon {
    color: #e74c3c;
}

.woo-custom-wishlist-btn.single-product.in-wishlist {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product .wishlist-text {
    font-weight: 500;
    margin-left: 4px;
}

.woo-custom-wishlist-btn.single-product .heart-icon,
.woo-custom-wishlist-btn.single-product .heart-icon-filled {
    font-size: 18px;
    margin-right: 4px;
}

/* Loading state */
.woo-custom-wishlist-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.woo-custom-wishlist-btn.loading .heart-icon,
.woo-custom-wishlist-btn.loading .heart-icon-filled {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Login required state */
.woo-custom-wishlist-btn.requires-login {
    opacity: 0.7;
}

.woo-custom-wishlist-btn.requires-login:hover {
    opacity: 0.9;
}

/* Notification styles */
.woo-custom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.woo-custom-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.woo-custom-notification.success {
    background: #27ae60;
}

.woo-custom-notification.error {
    background: #e74c3c;
}

/* My Account menu item icons - More specific selectors */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    display: inline-block !important;
    margin-left: 4px !important;
    text-align: center !important;
    line-height: 24px !important;
    font-size: 20px !important;
    vertical-align: middle !important;
    font-style: normal !important;
    font-weight: normal !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before {
    display: none !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    display: none !important;
}

/* Hover effects for menu icons */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before {
    display: none !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .woocommerce ul.products li.product .woo-custom-wishlist-btn {
        opacity: 1;
        transform: translateY(0);
        top: 5px;
        right: 5px;
        padding: 6px;
    }
    
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled {
        font-size: 18px;
    }
    
    .woo-custom-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .woo-custom-notification.show {
        transform: translateY(0);
    }
}

/* Product grid compatibility */
.woocommerce ul.products li.product {
    position: relative;
}

/* Theme compatibility adjustments */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    z-index: 10;
}

/* Ensure proper positioning in different themes */
.products .product .woo-custom-wishlist-btn,
.wc-block-grid__products .wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* WooCommerce Blocks compatibility */
.wc-block-grid__product {
    position: relative;
}

.wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: all 0.3s ease;
}

.wc-block-grid__product:hover .woo-custom-wishlist-btn {
    opacity: 1;
}

/* Accessibility improvements */
.woo-custom-wishlist-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon-filled {
    display: inline-block;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .woo-custom-wishlist-btn {
        border: 2px solid currentColor;
    }
    
    .woo-custom-wishlist-btn .heart-icon {
        color: currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .woo-custom-wishlist-btn,
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled,
    .woo-custom-notification {
        transition: none;
        animation: none;
    }
}

/* Hide specific dashboard elements */
#post-16 > div > div > div > p:nth-child(2),
#post-16 > div > div > div > p:nth-child(3) {
    display: none !important;
}

/* Dashboard Summary Styles */
.woo-custom-dashboard-summary {
    margin-bottom: 30px;
}

/* Welcome Section */
.woo-custom-welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
}

.woo-custom-welcome-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: white !important;
}

.woo-custom-welcome-subtitle {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
    color: white !important;
}

/* Summary Cards */
.woo-custom-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.woo-custom-summary-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.woo-custom-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.summary-card-content {
    flex: 1;
}

.summary-card-content h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.summary-card-number {
    font-size: 32px;
    font-weight: 700;
    color: #667eea;
    line-height: 1;
    margin-bottom: 5px;
}

.summary-card-content p {
    margin: 0 0 15px 0;
    color: #7f8c8d;
    font-size: 14px;
}

.summary-card-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.summary-card-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Recent Orders Section */
.woo-custom-recent-orders {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.woo-custom-recent-orders h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.recent-orders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recent-order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.order-number {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.order-date {
    color: #7f8c8d;
    font-size: 12px;
}

.order-status .status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-badge.status-pending {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.status-cancelled {
    background: #f5c6cb;
    color: #721c24;
}

.order-total {
    font-weight: 600;
    color: #667eea;
    font-size: 16px;
}

/* Dashboard Responsive Design */
@media (max-width: 768px) {
    .woo-custom-welcome-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .woo-custom-welcome-title {
        font-size: 22px;
    }

    .woo-custom-welcome-subtitle {
        font-size: 14px;
    }

    .woo-custom-summary-cards {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .woo-custom-summary-card {
        padding: 20px;
        text-align: center;
    }

    .summary-card-content {
        text-align: center;
    }

    .woo-custom-recent-orders {
        padding: 20px;
    }

    .recent-order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        padding: 15px;
    }

    .order-info {
        width: 100%;
    }

    .order-status,
    .order-total {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .woo-custom-welcome-section {
        padding: 15px;
    }

    .woo-custom-welcome-title {
        font-size: 20px;
    }

    .woo-custom-summary-card {
        padding: 15px;
    }

    .summary-card-number {
        font-size: 28px;
    }

    .woo-custom-recent-orders {
        padding: 15px;
    }
}

/* Orders Page Styling - Clean Table Design */
.woocommerce-account .woocommerce-MyAccount-content .shop_table.shop_table_responsive.my_account_orders {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e8ecf0;
}

/* Remove margin-top for specific table */
#post-16 > div > div > div > table {
    margin-top: 0 !important;
}

.woocommerce-account .shop_table.my_account_orders th,
.woocommerce-account .shop_table.my_account_orders td {
    padding: 20px 25px;
    text-align: left;
    border-bottom: 1px solid #f5f7fa;
    vertical-align: middle;
}

/* Specific column alignments */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-number {
    padding-left: 35px;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-actions {
    padding-right: 35px;
    text-align: right;
}

.woocommerce-account .shop_table.my_account_orders th:first-child {
    padding-left: 35px;
}

.woocommerce-account .shop_table.my_account_orders th:last-child {
    padding-right: 35px;
    text-align: right;
}

.woocommerce-account .shop_table.my_account_orders th {
    background: #f8fafc;
    color: #2d3748;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.woocommerce-account .shop_table.my_account_orders tbody tr {
    transition: all 0.3s ease;
    background: white;
}

.woocommerce-account .shop_table.my_account_orders tbody tr:hover {
    background: #f7fafc;
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.woocommerce-account .shop_table.my_account_orders tbody tr:last-child td {
    border-bottom: none;
}

/* Order Number Styling */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-number a {
    color: #4299e1;
    text-decoration: none;
    font-size: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 6px 12px;
    background: rgba(66, 153, 225, 0.1);
    border-radius: 6px;
    display: inline-block;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-number a:hover {
    background: #4299e1;
    color: white;
    transform: translateY(-1px);
}

/* Order Date Styling */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-date {
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

/* Order Status Styling */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-status .woocommerce-order-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
    border: none;
    display: inline-block;
    min-width: 100px;
    text-align: center;
    letter-spacing: 0.3px;
}

/* Status Colors - Clean Design */
.woocommerce-account .shop_table.my_account_orders .woocommerce-order-status.status-completed {
    background: #c6f6d5;
    color: #22543d;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-order-status.status-processing {
    background: #fef5e7;
    color: #744210;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-order-status.status-pending {
    background: #e6fffa;
    color: #234e52;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-order-status.status-cancelled {
    background: #fed7d7;
    color: #742a2a;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-order-status.status-on-hold {
    background: #ebf8ff;
    color: #2a4365;
}

/* Order Total Styling */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-total {
    font-weight: 700;
    font-size: 16px;
    color: #38a169;
}

/* Order Actions Styling */
.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-actions a {
    background: #4299e1;
    color: white !important;
    padding: 10px 18px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
    margin: 2px;
    border: none;
}

.woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-actions a:hover {
    background: #3182ce;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

/* Orders Table Responsive Design */
@media (max-width: 768px) {
    .woocommerce-account .shop_table.my_account_orders,
    .woocommerce-account .shop_table.my_account_orders thead,
    .woocommerce-account .shop_table.my_account_orders tbody,
    .woocommerce-account .shop_table.my_account_orders th,
    .woocommerce-account .shop_table.my_account_orders td,
    .woocommerce-account .shop_table.my_account_orders tr {
        display: block;
    }

    .woocommerce-account .shop_table.my_account_orders thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .woocommerce-account .shop_table.my_account_orders tr {
        border: 1px solid #e2e8f0;
        margin-bottom: 15px;
        padding: 20px;
        border-radius: 12px;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .woocommerce-account .shop_table.my_account_orders tr:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .woocommerce-account .shop_table.my_account_orders td {
        border: none;
        position: relative;
        padding: 12px 0 12px 45%;
        text-align: left;
    }

    .woocommerce-account .shop_table.my_account_orders td:before {
        content: attr(data-title) ": ";
        position: absolute;
        left: 0;
        width: 40%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: 600;
        color: #4a5568;
        font-size: 13px;
    }

    .woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-number a {
        padding: 6px 12px;
        font-size: 14px;
    }

    .woocommerce-account .shop_table.my_account_orders .woocommerce-orders-table__cell-order-actions a {
        padding: 8px 16px;
        font-size: 12px;
    }

    .woocommerce-account .shop_table.my_account_orders .woocommerce-order-status {
        padding: 6px 12px;
        font-size: 11px;
        min-width: 80px;
    }
}

/* Additional styling for better visual hierarchy */
.woocommerce-account .woocommerce-MyAccount-content > h2 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 3px solid #667eea;
    position: relative;
}

.woocommerce-account .woocommerce-MyAccount-content > h2:after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Empty state styling */
.woocommerce-account .woocommerce-message {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border: 1px solid #667eea;
    border-radius: 12px;
    padding: 20px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.woocommerce-account .woocommerce-message:before {
    color: #667eea;
}

/* My Reviews Page Styles */
.woo-custom-my-reviews {
    max-width: 1200px;
    margin: 0 auto;
}

.reviews-header {
    margin-bottom: 30px;
    text-align: center;
}

.reviews-header h2 {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.reviews-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.review-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.review-product-info h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.review-product-info h4 a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.review-product-info h4 a:hover {
    color: #764ba2;
}

.review-date {
    color: #7f8c8d;
    font-size: 14px;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #ffc107;
    font-size: 16px;
}

.star.empty {
    color: #ddd;
}

.verified-badge {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.review-content {
    margin: 15px 0;
    color: #2c3e50;
    line-height: 1.6;
}

.review-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.edit-review-btn,
.delete-review-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.edit-review-btn {
    background: #667eea;
    color: white;
}

.edit-review-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.delete-review-btn {
    background: #e53e3e;
    color: white;
}

.delete-review-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.delete-review-btn:disabled,
.edit-review-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.reviews-empty {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.reviews-empty p {
    color: #7f8c8d;
    font-size: 16px;
    margin-bottom: 15px;
}

.reviews-empty .button {
    background: #667eea;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.reviews-empty .button:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Responsive Design for Reviews */
@media (max-width: 768px) {
    .reviews-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .review-item {
        padding: 20px;
    }

    .review-header {
        flex-direction: column;
        gap: 10px;
    }

    .review-actions {
        flex-direction: column;
        gap: 8px;
    }

    .edit-review-btn,
    .delete-review-btn {
        text-align: center;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .reviews-stats {
        grid-template-columns: 1fr;
    }

    .review-item {
        padding: 15px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-number {
        font-size: 28px;
    }
}
