<?php
/**
 * Test file for review delete functionality
 * This file can be used to test the review deletion feature
 */

// Test the WooCustom_Reviews class
if (class_exists('WooCustom_Reviews')) {
    echo "✓ WooCustom_Reviews class exists\n";
    
    $reviews_instance = WooCustom_Reviews::instance();
    
    // Check if delete handler method exists
    if (method_exists($reviews_instance, 'handle_delete_review')) {
        echo "✓ handle_delete_review method exists\n";
    } else {
        echo "✗ handle_delete_review method missing\n";
    }
    
    // Check if AJAX hooks are registered
    if (has_action('wp_ajax_delete_review')) {
        echo "✓ wp_ajax_delete_review hook registered\n";
    } else {
        echo "✗ wp_ajax_delete_review hook missing\n";
    }
    
    if (has_action('wp_ajax_nopriv_delete_review')) {
        echo "✓ wp_ajax_nopriv_delete_review hook registered\n";
    } else {
        echo "✗ wp_ajax_nopriv_delete_review hook missing\n";
    }
    
} else {
    echo "✗ WooCustom_Reviews class not found\n";
}

// Check if template file exists and has delete button
$template_path = WOO_CUSTOM_PLUGIN_DIR . 'templates/myaccount/my-reviews.php';
if (file_exists($template_path)) {
    echo "✓ Template file exists\n";
    
    $template_content = file_get_contents($template_path);
    
    if (strpos($template_content, 'delete-review-btn') !== false) {
        echo "✓ Delete button found in template\n";
    } else {
        echo "✗ Delete button missing in template\n";
    }
    
    if (strpos($template_content, 'delete_review') !== false) {
        echo "✓ Delete AJAX action found in template\n";
    } else {
        echo "✗ Delete AJAX action missing in template\n";
    }
    
} else {
    echo "✗ Template file not found\n";
}

// Check CSS file for delete button styles
$css_path = WOO_CUSTOM_PLUGIN_DIR . 'assets/css/woo-custom.css';
if (file_exists($css_path)) {
    echo "✓ CSS file exists\n";
    
    $css_content = file_get_contents($css_path);
    
    if (strpos($css_content, '.delete-review-btn') !== false) {
        echo "✓ Delete button styles found in CSS\n";
    } else {
        echo "✗ Delete button styles missing in CSS\n";
    }
    
} else {
    echo "✗ CSS file not found\n";
}

echo "\nTest completed!\n";
?>
