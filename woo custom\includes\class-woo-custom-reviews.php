<?php
/**
 * My Reviews functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Reviews {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX hooks for updating reviews
        add_action('wp_ajax_update_review', array($this, 'handle_update_review'));
        add_action('wp_ajax_nopriv_update_review', array($this, 'handle_update_review'));

        // AJAX hooks for deleting reviews
        add_action('wp_ajax_delete_review', array($this, 'handle_delete_review'));
        add_action('wp_ajax_nopriv_delete_review', array($this, 'handle_delete_review'));

        // Add profile photos to reviews
        add_filter('get_avatar', array($this, 'custom_review_avatar'), 10, 5);
    }
    
    /**
     * Get user's product reviews
     */
    public function get_user_reviews($user_id = null, $limit = -1, $offset = 0) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return array();
        }
        
        $args = array(
            'user_id' => $user_id,
            'post_type' => 'product',
            'status' => 'approve',
            'type' => 'review',
            'orderby' => 'comment_date',
            'order' => 'DESC'
        );
        
        if ($limit > 0) {
            $args['number'] = $limit;
            $args['offset'] = $offset;
        }
        
        $comments = get_comments($args);
        
        $reviews = array();
        foreach ($comments as $comment) {
            $product = wc_get_product($comment->comment_post_ID);
            if ($product && $product->exists()) {
                $rating = get_comment_meta($comment->comment_ID, 'rating', true);
                $verified = get_comment_meta($comment->comment_ID, 'verified', true);
                
                $reviews[] = array(
                    'comment' => $comment,
                    'product' => $product,
                    'rating' => $rating ? intval($rating) : 0,
                    'verified' => $verified ? true : false,
                    'date' => $comment->comment_date,
                    'content' => $comment->comment_content
                );
            }
        }
        
        return $reviews;
    }
    
    /**
     * Get user's reviews count
     */
    public function get_user_reviews_count($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return 0;
        }
        
        $args = array(
            'user_id' => $user_id,
            'post_type' => 'product',
            'status' => 'approve',
            'type' => 'review',
            'count' => true
        );
        
        return get_comments($args);
    }
    
    /**
     * Get user's average rating
     */
    public function get_user_average_rating($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return 0;
        }
        
        $reviews = $this->get_user_reviews($user_id);
        
        if (empty($reviews)) {
            return 0;
        }
        
        $total_rating = 0;
        $count = 0;
        
        foreach ($reviews as $review) {
            if ($review['rating'] > 0) {
                $total_rating += $review['rating'];
                $count++;
            }
        }
        
        return $count > 0 ? round($total_rating / $count, 1) : 0;
    }
    
    /**
     * Get rating distribution for user
     */
    public function get_user_rating_distribution($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return array();
        }
        
        $reviews = $this->get_user_reviews($user_id);
        
        $distribution = array(
            5 => 0,
            4 => 0,
            3 => 0,
            2 => 0,
            1 => 0
        );
        
        foreach ($reviews as $review) {
            if ($review['rating'] > 0 && $review['rating'] <= 5) {
                $distribution[$review['rating']]++;
            }
        }
        
        return $distribution;
    }
    
    /**
     * Format review date
     */
    public function format_review_date($date) {
        return date_i18n(get_option('date_format'), strtotime($date));
    }
    
    /**
     * Get star rating HTML
     */
    public function get_star_rating_html($rating) {
        if ($rating <= 0) {
            return '';
        }
        
        $rating = min(5, max(1, intval($rating)));
        
        $html = '<div class="woo-custom-star-rating">';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $html .= '<span class="star filled">★</span>';
            } else {
                $html .= '<span class="star">☆</span>';
            }
        }
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Handle AJAX review update
     */
    public function handle_update_review() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Güvenlik doğrulaması başarısız.')
            )));
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Bu işlem için giriş yapmanız gerekiyor.')
            )));
        }

        // Get form data
        $comment_id = intval($_POST['comment_id']);
        $product_id = intval($_POST['product_id']);
        $rating = intval($_POST['rating']);
        $content = sanitize_textarea_field($_POST['content']);

        // Validate data
        if (!$comment_id || !$product_id || !$rating || !$content) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Tüm alanları doldurun.')
            )));
        }

        if ($rating < 1 || $rating > 5) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Geçersiz puan değeri.')
            )));
        }

        // Get comment
        $comment = get_comment($comment_id);
        if (!$comment) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Değerlendirme bulunamadı.')
            )));
        }

        // Check if user owns this comment
        $current_user_id = get_current_user_id();
        if ($comment->user_id != $current_user_id) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Bu değerlendirmeyi güncelleme yetkiniz yok.')
            )));
        }

        // Update comment
        $comment_data = array(
            'comment_ID' => $comment_id,
            'comment_content' => $content,
        );

        $result = wp_update_comment($comment_data);

        if ($result === false) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Değerlendirme güncellenirken bir hata oluştu.')
            )));
        }

        // Update rating meta
        update_comment_meta($comment_id, 'rating', $rating);

        // Clear product rating cache
        delete_transient('wc_average_rating_' . $product_id);
        delete_transient('woocommerce_product_' . $product_id);

        // Update product rating average
        $this->update_product_rating_average($product_id);

        wp_die(json_encode(array(
            'success' => true,
            'data' => array('message' => 'Değerlendirmeniz başarıyla güncellendi.')
        )));
    }

    /**
     * Handle AJAX review delete
     */
    public function handle_delete_review() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Güvenlik doğrulaması başarısız.')
            )));
        }

        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Bu işlem için giriş yapmanız gerekiyor.')
            )));
        }

        // Get form data
        $comment_id = intval($_POST['comment_id']);

        // Validate data
        if (!$comment_id) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Geçersiz değerlendirme ID.')
            )));
        }

        // Get comment
        $comment = get_comment($comment_id);
        if (!$comment) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Değerlendirme bulunamadı.')
            )));
        }

        // Check if user owns this comment
        $current_user_id = get_current_user_id();
        if ($comment->user_id != $current_user_id) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Bu değerlendirmeyi silme yetkiniz yok.')
            )));
        }

        // Get product ID before deleting
        $product_id = $comment->comment_post_ID;

        // Delete comment and its meta
        $result = wp_delete_comment($comment_id, true);

        if ($result === false) {
            wp_die(json_encode(array(
                'success' => false,
                'data' => array('message' => 'Değerlendirme silinirken bir hata oluştu.')
            )));
        }

        // Clear product rating cache
        delete_transient('wc_average_rating_' . $product_id);
        delete_transient('woocommerce_product_' . $product_id);

        // Update product rating average
        $this->update_product_rating_average($product_id);

        wp_die(json_encode(array(
            'success' => true,
            'data' => array('message' => 'Değerlendirmeniz başarıyla silindi.')
        )));
    }

    /**
     * Update product rating average
     */
    private function update_product_rating_average($product_id) {
        global $wpdb;

        $rating_average = $wpdb->get_var($wpdb->prepare("
            SELECT AVG(meta_value) FROM {$wpdb->commentmeta} cm
            LEFT JOIN {$wpdb->comments} c ON c.comment_ID = cm.comment_id
            WHERE c.comment_post_ID = %d
            AND c.comment_approved = '1'
            AND cm.meta_key = 'rating'
            AND cm.meta_value > 0
        ", $product_id));

        $rating_count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$wpdb->commentmeta} cm
            LEFT JOIN {$wpdb->comments} c ON c.comment_ID = cm.comment_id
            WHERE c.comment_post_ID = %d
            AND c.comment_approved = '1'
            AND cm.meta_key = 'rating'
            AND cm.meta_value > 0
        ", $product_id));

        update_post_meta($product_id, '_wc_average_rating', $rating_average ? round($rating_average, 2) : 0);
        update_post_meta($product_id, '_wc_review_count', $rating_count ? $rating_count : 0);
    }

    /**
     * Custom avatar for reviews with profile photos
     */
    public function custom_review_avatar($avatar, $id_or_email, $size, $default, $alt) {
        // Only apply on product pages and review contexts
        if (!is_product() && !is_woocommerce()) {
            return $avatar;
        }

        $user = false;

        // Get user from different input types
        if (is_numeric($id_or_email)) {
            $user = get_user_by('id', (int) $id_or_email);
        } elseif (is_object($id_or_email)) {
            if (!empty($id_or_email->user_id)) {
                $user = get_user_by('id', (int) $id_or_email->user_id);
            }
        } elseif (is_string($id_or_email)) {
            $user = get_user_by('email', $id_or_email);
        }

        if (!$user) {
            return $avatar;
        }

        // Get custom profile photo
        $profile_photo_id = get_user_meta($user->ID, '_tutor_profile_photo', true);

        if ($profile_photo_id) {
            $profile_photo_url = wp_get_attachment_image_url($profile_photo_id, array($size, $size));

            if ($profile_photo_url) {
                $avatar = sprintf(
                    '<img alt="%s" src="%s" class="avatar avatar-%d photo" height="%d" width="%d" />',
                    esc_attr($alt),
                    esc_url($profile_photo_url),
                    esc_attr($size),
                    esc_attr($size),
                    esc_attr($size)
                );
            }
        }

        return $avatar;
    }
}
