<?php
/**
 * WooCommerce Profile Photos Management
 * Integrates Tutor LMS style profile and cover photo functionality into WooCommerce My Account
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Profile_Photos {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Profile photo meta key
     */
    const PROFILE_PHOTO_META = '_tutor_profile_photo';

    /**
     * Cover photo meta key
     */
    const COVER_PHOTO_META = '_tutor_cover_photo';

    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add profile photos section to WooCommerce edit account form (at the top)
        add_action('woocommerce_edit_account_form_start', array($this, 'add_profile_photos_section'));

        // Add AJAX handlers for photo upload and delete
        add_action('wp_ajax_woo_custom_user_photo_upload', array($this, 'handle_photo_upload'));
        add_action('wp_ajax_woo_custom_user_photo_remove', array($this, 'handle_photo_remove'));

        // Enqueue scripts and styles for profile photos
        add_action('wp_enqueue_scripts', array($this, 'enqueue_profile_photo_assets'));
    }

    /**
     * Add profile photos section to WooCommerce edit account form
     */
    public function add_profile_photos_section() {
        $user = wp_get_current_user();

        // Get current photos
        $profile_photo_id = get_user_meta($user->ID, self::PROFILE_PHOTO_META, true);
        $cover_photo_id = get_user_meta($user->ID, self::COVER_PHOTO_META, true);

        // Prepare photo URLs
        $profile_placeholder = WOO_CUSTOM_PLUGIN_URL . 'assets/images/profile-placeholder.svg';
        $cover_placeholder = WOO_CUSTOM_PLUGIN_URL . 'assets/images/cover-placeholder.svg';

        $profile_photo_src = $profile_placeholder;
        if ($profile_photo_id) {
            $url = wp_get_attachment_image_url($profile_photo_id, 'full');
            if (!empty($url)) {
                $profile_photo_src = $url;
            }
        }

        $cover_photo_src = $cover_placeholder;
        if ($cover_photo_id) {
            $url = wp_get_attachment_image_url($cover_photo_id, 'full');
            if (!empty($url)) {
                $cover_photo_src = $url;
            }
        }

        // Get max file size
        $max_filesize = wp_max_upload_size();

        // Include the profile photos template
        wc_get_template('myaccount/profile-photos.php', array(
            'profile_photo_src' => $profile_photo_src,
            'cover_photo_src' => $cover_photo_src,
            'profile_placeholder' => $profile_placeholder,
            'cover_placeholder' => $cover_placeholder,
            'profile_photo_id' => $profile_photo_id,
            'cover_photo_id' => $cover_photo_id,
            'max_filesize' => $max_filesize
        ), '', WOO_CUSTOM_PLUGIN_DIR . 'templates/');
    }

    /**
     * Handle photo upload via AJAX
     */
    public function handle_photo_upload() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
            wp_die(__('Güvenlik kontrolü başarısız.', 'woo-custom'));
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Giriş yapmalısınız.', 'woo-custom')));
        }

        $photo_type = sanitize_text_field($_POST['photo_type']);
        $meta_key = ($photo_type === 'cover_photo') ? self::COVER_PHOTO_META : self::PROFILE_PHOTO_META;

        // Handle file upload
        $photo = $_FILES['photo_file'] ?? null;
        if (!$photo || !$photo['size']) {
            wp_send_json_error(array('message' => __('Lütfen bir fotoğraf seçin.', 'woo-custom')));
        }

        // Check file type
        if (strpos($photo['type'], 'image') === false) {
            wp_send_json_error(array('message' => __('Sadece resim dosyaları yüklenebilir.', 'woo-custom')));
        }

        // Handle upload
        if (!function_exists('wp_handle_upload')) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        $upload_overrides = array('test_form' => false);
        $movefile = wp_handle_upload($photo, $upload_overrides);

        if ($movefile && !isset($movefile['error'])) {
            $file_path = $movefile['file'];
            $file_url = $movefile['url'];

            // Get mime type
            $mime_type = '';
            if (file_exists($file_path)) {
                $image_info = getimagesize($file_path);
                $mime_type = is_array($image_info) && count($image_info) ? $image_info['mime'] : '';
            }

            // Create attachment
            $media_id = wp_insert_attachment(array(
                'guid' => $file_path,
                'post_mime_type' => $mime_type,
                'post_title' => preg_replace('/\.[^.]+$/', '', basename($file_url)),
                'post_content' => '',
                'post_status' => 'inherit',
            ), $file_path, 0);

            if ($media_id) {
                // Generate attachment metadata
                require_once ABSPATH . 'wp-admin/includes/image.php';
                wp_update_attachment_metadata($media_id, wp_generate_attachment_metadata($media_id, $file_path));

                // Delete existing photo
                $this->delete_existing_user_photo($user_id, $photo_type);

                // Update user meta
                update_user_meta($user_id, $meta_key, $media_id);

                wp_send_json_success(array(
                    'message' => __('Fotoğraf başarıyla yüklendi.', 'woo-custom'),
                    'photo_url' => wp_get_attachment_image_url($media_id, 'full')
                ));
            }
        }

        wp_send_json_error(array('message' => __('Fotoğraf yüklenirken bir hata oluştu.', 'woo-custom')));
    }

    /**
     * Handle photo removal via AJAX
     */
    public function handle_photo_remove() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
            wp_die(__('Güvenlik kontrolü başarısız.', 'woo-custom'));
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Giriş yapmalısınız.', 'woo-custom')));
        }

        $photo_type = sanitize_text_field($_POST['photo_type']);
        $this->delete_existing_user_photo($user_id, $photo_type);

        wp_send_json_success(array('message' => __('Fotoğraf başarıyla silindi.', 'woo-custom')));
    }

    /**
     * Delete existing user photo
     */
    private function delete_existing_user_photo($user_id, $type) {
        $meta_key = ($type === 'cover_photo') ? self::COVER_PHOTO_META : self::PROFILE_PHOTO_META;
        $photo_id = get_user_meta($user_id, $meta_key, true);

        if (is_numeric($photo_id)) {
            wp_delete_attachment($photo_id, true);
        }

        delete_user_meta($user_id, $meta_key);
    }

    /**
     * Enqueue profile photo assets
     */
    public function enqueue_profile_photo_assets() {
        // Only enqueue on my account pages
        if (!is_account_page()) {
            return;
        }

        wp_enqueue_style(
            'woo-custom-profile-photos',
            WOO_CUSTOM_PLUGIN_URL . 'assets/css/profile-photos.css',
            array(),
            WOO_CUSTOM_VERSION
        );

        wp_enqueue_script(
            'woo-custom-profile-photos',
            WOO_CUSTOM_PLUGIN_URL . 'assets/js/profile-photos.js',
            array('jquery'),
            WOO_CUSTOM_VERSION,
            true
        );

        // Localize script
        wp_localize_script('woo-custom-profile-photos', 'woo_custom_profile_photos', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_custom_nonce'),
            'i18n' => array(
                'uploading' => __('Yükleniyor...', 'woo-custom'),
                'upload_error' => __('Yükleme hatası', 'woo-custom'),
                'delete_confirm' => __('Bu fotoğrafı silmek istediğinizden emin misiniz?', 'woo-custom'),
                'deleting' => __('Siliniyor...', 'woo-custom'),
                'delete_error' => __('Silme hatası', 'woo-custom'),
            )
        ));
    }

    /**
     * Get user profile photo URL
     */
    public static function get_user_profile_photo_url($user_id) {
        $photo_id = get_user_meta($user_id, self::PROFILE_PHOTO_META, true);
        if ($photo_id) {
            return wp_get_attachment_image_url($photo_id, 'full');
        }
        return WOO_CUSTOM_PLUGIN_URL . 'assets/images/profile-placeholder.svg';
    }

    /**
     * Get user cover photo URL
     */
    public static function get_user_cover_photo_url($user_id) {
        $photo_id = get_user_meta($user_id, self::COVER_PHOTO_META, true);
        if ($photo_id) {
            return wp_get_attachment_image_url($photo_id, 'full');
        }
        return WOO_CUSTOM_PLUGIN_URL . 'assets/images/cover-placeholder.svg';
    }
}