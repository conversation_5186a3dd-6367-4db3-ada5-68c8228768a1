/**
 * WooCustom Featured Video Frontend Styles
 * 
 * @package WooCustom
 * @since 1.0.0
 */

/* Video in gallery integration */
.woo-custom-featured-video-gallery {
    position: relative;
    width: 100%;
}

.woo-custom-featured-video {
    width: 100%;
    max-width: 100%;
    margin: 0;
}

.woo-custom-featured-video .video-container {
    position: relative;
    width: 100%;
    height: 0;
    overflow: hidden;
    border-radius: 8px;
    background: #000;
}

/* Gallery specific aspect ratios */
.woocommerce-product-gallery .woo-custom-featured-video.aspect-16-9 .video-container {
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.woocommerce-product-gallery .woo-custom-featured-video.aspect-4-3 .video-container {
    padding-bottom: 75%; /* 4:3 aspect ratio */
}

.woo-custom-featured-video .video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Gallery thumbnail styling */
.woocommerce-product-gallery__trigger {
    z-index: 99;
}

/* Ensure video fits properly in gallery */
.woocommerce-product-gallery .woo-custom-featured-video-gallery {
    display: block;
    width: 100%;
    height: auto;
}

/* Flexslider compatibility */
.flex-viewport .woo-custom-featured-video {
    width: 100%;
    height: auto;
}

.flex-viewport .woo-custom-featured-video .video-container {
    border-radius: 10px;
}

/* Lightbox compatibility - hide video in lightbox */
.pswp .woo-custom-featured-video {
    display: none;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .woo-custom-featured-video .video-container {
        border-radius: 6px;
    }
    
    .woo-custom-featured-video .video-container iframe {
        border-radius: 6px;
    }
}

@media (max-width: 480px) {
    .woo-custom-featured-video .video-container {
        border-radius: 4px;
    }
    
    .woo-custom-featured-video .video-container iframe {
        border-radius: 4px;
    }
}

/* Theme compatibility adjustments */
.single-product .woocommerce-product-gallery .woo-custom-featured-video {
    margin: 0;
    padding: 0;
}

/* Ensure proper sizing in different gallery layouts */
.woocommerce-product-gallery__wrapper .woo-custom-featured-video-gallery {
    width: 100%;
    display: block;
}

/* Hide video controls overlay when not needed */
.woo-custom-featured-video .video-container:hover {
    /* Add any hover effects here if needed */
}

/* Loading state */
.woo-custom-featured-video.loading .video-container {
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woo-custom-featured-video.loading .video-container:before {
    content: "Video yükleniyor...";
    color: #666;
    font-size: 14px;
}

/* Error state */
.woo-custom-featured-video.error .video-container {
    background: #f8f8f8;
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.woo-custom-featured-video.error .video-container:before {
    content: "Video yüklenemedi";
    color: #999;
    font-size: 14px;
}
